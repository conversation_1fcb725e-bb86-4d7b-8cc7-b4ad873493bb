import { IconSymbol } from '@/components/ui/IconSymbol';
import { BorderRadius, Colors, Shadows, Spacing, Typography } from '@/constants/Theme';
import { mockPlantsInCollection, mockRecentScans } from '@/data/mockData';
import { router } from 'expo-router';
import React from 'react';
import {
    Alert,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

export default function ProfileScreen() {
  const totalPlants = mockPlantsInCollection.length;
  const healthyPlants = mockPlantsInCollection.filter(plant => plant.status === 'healthy').length;
  const totalScans = mockRecentScans.length;
  const averageHealth = Math.round(
    mockPlantsInCollection.reduce((sum, plant) => sum + plant.health, 0) / totalPlants
  );

  const handleSettingsPress = () => {
    Alert.alert('Settings', 'Settings screen coming soon!');
  };

  const handleHelpPress = () => {
    Alert.alert('Help & Support', 'Help & Support coming soon!');
  };

  const handleAboutPress = () => {
    Alert.alert('About', 'Plant Identifier v1.0.0\nBuilt with Expo React Native');
  };

  const handleLogoutPress = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: () => {
          // Handle logout logic here
          Alert.alert('Logged out', 'You have been logged out successfully');
        }},
      ]
    );
  };

  const menuItems = [
    {
      icon: 'gear',
      title: 'Settings',
      subtitle: 'App preferences and notifications',
      onPress: handleSettingsPress,
    },
    {
      icon: 'questionmark.circle',
      title: 'Help & Support',
      subtitle: 'Get help and contact support',
      onPress: handleHelpPress,
    },
    {
      icon: 'info.circle',
      title: 'About',
      subtitle: 'App version and information',
      onPress: handleAboutPress,
    },
    {
      icon: 'rectangle.portrait.and.arrow.right',
      title: 'Logout',
      subtitle: 'Sign out of your account',
      onPress: handleLogoutPress,
      isDestructive: true,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.profileInfo}>
            <View style={styles.avatar}>
              <IconSymbol name="person.fill" size={32} color={Colors.primary} />
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>Plant Lover</Text>
              <Text style={styles.userEmail}><EMAIL></Text>
            </View>
          </View>
          <TouchableOpacity style={styles.editButton}>
            <IconSymbol name="pencil" size={20} color={Colors.text} />
          </TouchableOpacity>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Your Garden Stats</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <IconSymbol name="leaf.fill" size={24} color={Colors.primary} />
              <Text style={styles.statNumber}>{totalPlants}</Text>
              <Text style={styles.statLabel}>Total Plants</Text>
            </View>
            
            <View style={styles.statCard}>
              <IconSymbol name="heart.fill" size={24} color={Colors.success} />
              <Text style={styles.statNumber}>{healthyPlants}</Text>
              <Text style={styles.statLabel}>Healthy Plants</Text>
            </View>
            
            <View style={styles.statCard}>
              <IconSymbol name="camera.fill" size={24} color={Colors.primary} />
              <Text style={styles.statNumber}>{totalScans}</Text>
              <Text style={styles.statLabel}>Plants Scanned</Text>
            </View>
            
            <View style={styles.statCard}>
              <IconSymbol name="chart.bar.fill" size={24} color={Colors.warning} />
              <Text style={styles.statNumber}>{averageHealth}%</Text>
              <Text style={styles.statLabel}>Avg Health</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity 
              style={styles.quickActionCard} 
              onPress={() => router.push('/(tabs)/scan')}
            >
              <IconSymbol name="camera.fill" size={24} color={Colors.primary} />
              <Text style={styles.quickActionText}>Scan Plant</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={() => router.push('/diagnose')}
            >
              <IconSymbol name="stethoscope" size={24} color={Colors.primary} />
              <Text style={styles.quickActionText}>Diagnose</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quickActionCard}
              onPress={() => router.push('/(tabs)/collection')}
            >
              <IconSymbol name="leaf.fill" size={24} color={Colors.primary} />
              <Text style={styles.quickActionText}>My Plants</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={item.onPress}
            >
              <View style={styles.menuItemLeft}>
                <View style={[styles.menuIcon, item.isDestructive && styles.menuIconDestructive]}>
                  <IconSymbol
                    name={item.icon as any}
                    size={20}
                    color={item.isDestructive ? Colors.error : Colors.primary}
                  />
                </View>
                <View style={styles.menuItemText}>
                  <Text style={[styles.menuItemTitle, item.isDestructive && styles.menuItemTitleDestructive]}>
                    {item.title}
                  </Text>
                  <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
                </View>
              </View>
              <IconSymbol name="chevron.right" size={16} color={Colors.textMuted} />
            </TouchableOpacity>
          ))}
        </View>

        {/* App Version */}
        <View style={styles.footer}>
          <Text style={styles.versionText}>Plant Identifier v1.0.0</Text>
          <Text style={styles.copyrightText}>© 2024 Plant Identifier App</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    paddingTop: Spacing.xl,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  statsContainer: {
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    alignItems: 'center',
    ...Shadows.sm,
  },
  statNumber: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.bold,
    color: Colors.text,
    marginTop: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  quickActionCard: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    alignItems: 'center',
    ...Shadows.sm,
  },
  quickActionText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    ...Shadows.sm,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  menuIconDestructive: {
    backgroundColor: Colors.error + '20',
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.medium,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  menuItemTitleDestructive: {
    color: Colors.error,
  },
  menuItemSubtitle: {
    fontSize: Typography.sizes.sm,
    color: Colors.textSecondary,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  versionText: {
    fontSize: Typography.sizes.sm,
    color: Colors.textMuted,
    marginBottom: Spacing.xs,
  },
  copyrightText: {
    fontSize: Typography.sizes.xs,
    color: Colors.textMuted,
  },
});
