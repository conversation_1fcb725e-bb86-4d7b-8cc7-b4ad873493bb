import 'dotenv/config';

export default {
  expo: {
    name: 'expo-mvp-plant-identifier-270725',
    slug: 'expo-mvp-plant-identifier-270725',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/images/icon.png',
    scheme: 'expomvpplantidentifier270725',
    userInterfaceStyle: 'automatic',
    newArchEnabled: true,
    ios: {
      supportsTablet: true,
      infoPlist: {
        NSCameraUsageDescription: 'This app uses the camera to identify and diagnose plants.',
        NSPhotoLibraryUsageDescription: 'This app accesses the photo library to select plant images for identification and diagnosis.',
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/images/adaptive-icon.png',
        backgroundColor: '#ffffff',
      },
      edgeToEdgeEnabled: true,
      permissions: [
        'android.permission.CAMERA',
        'android.permission.READ_EXTERNAL_STORAGE',
        'android.permission.WRITE_EXTERNAL_STORAGE',
      ],
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/favicon.png',
    },
    plugins: [
      'expo-router',
      [
        'expo-splash-screen',
        {
          image: './assets/images/splash-icon.png',
          imageWidth: 200,
          resizeMode: 'contain',
          backgroundColor: '#ffffff',
        },
      ],
      [
        'expo-image-picker',
        {
          photosPermission: 'The app accesses your photos to let you select plant images for identification and diagnosis.',
          cameraPermission: 'The app accesses your camera to let you take photos of plants for identification and diagnosis.',
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
      OPENROUTER_API_URL: process.env.OPENROUTER_API_URL,
      OPENROUTER_MODEL: process.env.OPENROUTER_MODEL,
    },
  },
};
