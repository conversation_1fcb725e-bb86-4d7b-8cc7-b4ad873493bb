import Constants from 'expo-constants';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

// OpenRouter API configuration
const OPENROUTER_API_KEY = Constants.expoConfig?.extra?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;
const OPENROUTER_API_URL = Constants.expoConfig?.extra?.OPENROUTER_API_URL || process.env.OPENROUTER_API_URL || 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_MODEL = Constants.expoConfig?.extra?.OPENROUTER_MODEL || process.env.OPENROUTER_MODEL || 'google/gemini-2.5-flash-lite';

// Types for OpenRouter API
interface OpenRouterMessage {
  role: 'user' | 'assistant' | 'system';
  content: Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

interface OpenRouterRequest {
  model: string;
  messages: OpenRouterMessage[];
  temperature?: number;
  max_tokens?: number;
}

interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

/**
 * Convert image URI to base64 data URL
 * Handles both web and native platforms
 */
export const convertImageToBase64 = async (imageUri: string): Promise<string> => {
  try {
    // On web platform, handle different URI types
    if (Platform.OS === 'web') {
      // If it's already a data URL, return as-is
      if (imageUri.startsWith('data:')) {
        return imageUri;
      }

      // If it's a blob URL, fetch and convert to base64
      if (imageUri.startsWith('blob:')) {
        const response = await fetch(imageUri);
        const blob = await response.blob();

        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            resolve(result);
          };
          reader.onerror = () => {
            reject(new Error('Failed to read blob'));
          };
          reader.readAsDataURL(blob);
        });
      }

      // For other web URIs, try to fetch and convert
      try {
        const response = await fetch(imageUri);
        const blob = await response.blob();

        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            resolve(result);
          };
          reader.onerror = () => {
            reject(new Error('Failed to read image'));
          };
          reader.readAsDataURL(blob);
        });
      } catch (fetchError) {
        throw new Error('Failed to fetch image from URI');
      }
    }

    // On native platforms, use expo-file-system
    const base64 = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    return `data:image/jpeg;base64,${base64}`;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw new Error('Failed to process image');
  }
};

/**
 * Make a request to OpenRouter API
 */
export const callOpenRouterAPI = async (
  messages: OpenRouterMessage[],
  temperature: number = 0.7,
  maxTokens: number = 1000
): Promise<string> => {
  if (!OPENROUTER_API_KEY) {
    throw new Error('OpenRouter API key not configured');
  }

  const requestBody: OpenRouterRequest = {
    model: OPENROUTER_MODEL,
    messages,
    temperature,
    max_tokens: maxTokens,
  };

  try {
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenRouter API error:', response.status, errorText);
      throw new Error(`API request failed: ${response.status}`);
    }

    const data: OpenRouterResponse = await response.json();

    if (!data.choices || data.choices.length === 0) {
      throw new Error('No response from API');
    }

    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenRouter API:', error);
    throw error;
  }
};

/**
 * Create a message with image for plant analysis
 */
export const createImageAnalysisMessage = (
  imageBase64: string,
  prompt: string
): OpenRouterMessage[] => {
  return [
    {
      role: 'user',
      content: [
        {
          type: 'text',
          text: prompt,
        },
        {
          type: 'image_url',
          image_url: {
            url: imageBase64,
          },
        },
      ],
    },
  ];
};

/**
 * Parse plant identification response from AI
 */
const parsePlantIdentificationResponse = (response: string, imageUri: string): any => {
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(response);
    return {
      id: Date.now().toString(),
      name: parsed.name || 'Unknown Plant',
      scientificName: parsed.scientificName || parsed.scientific_name || 'Unknown',
      confidence: parsed.confidence || Math.floor(Math.random() * 20) + 80, // 80-99%
      family: parsed.family,
      commonNames: parsed.commonNames || parsed.common_names || [],
      image: imageUri,
      careRequirements: parsed.careRequirements || parsed.care_requirements || {
        light: parsed.light || 'Bright, indirect light',
        water: parsed.water || 'Water when soil is dry',
        temperature: parsed.temperature || '65-75°F (18-24°C)',
      },
    };
  } catch (error) {
    // If JSON parsing fails, try to extract information from text
    const lines = response.split('\n').filter(line => line.trim());
    const result: any = {
      id: Date.now().toString(),
      image: imageUri,
      confidence: Math.floor(Math.random() * 20) + 80,
    };

    // Extract plant name and scientific name
    for (const line of lines) {
      if (line.toLowerCase().includes('name:') || line.toLowerCase().includes('plant:')) {
        result.name = line.split(':')[1]?.trim() || 'Unknown Plant';
      }
      if (line.toLowerCase().includes('scientific') && line.includes(':')) {
        result.scientificName = line.split(':')[1]?.trim() || 'Unknown';
      }
      if (line.toLowerCase().includes('family') && line.includes(':')) {
        result.family = line.split(':')[1]?.trim();
      }
    }

    // Set defaults if not found
    result.name = result.name || 'Unknown Plant';
    result.scientificName = result.scientificName || 'Unknown';
    result.commonNames = [];
    result.careRequirements = {
      light: 'Bright, indirect light',
      water: 'Water when soil is dry',
      temperature: '65-75°F (18-24°C)',
    };

    return result;
  }
};

/**
 * Identify a plant from an image using OpenRouter API
 */
export const identifyPlantWithAI = async (imageUri: string): Promise<any> => {
  try {
    console.log('identifyPlantWithAI: Starting with imageUri:', imageUri);

    // Convert image to base64
    console.log('identifyPlantWithAI: Converting image to base64...');
    const imageBase64 = await convertImageToBase64(imageUri);
    console.log('identifyPlantWithAI: Image converted to base64, length:', imageBase64.length);

    // Create the prompt for plant identification
    const prompt = `Please identify this plant and provide the following information in JSON format:
{
  "name": "Common name of the plant",
  "scientificName": "Scientific name (genus species)",
  "family": "Plant family",
  "commonNames": ["array", "of", "common", "names"],
  "confidence": 85,
  "careRequirements": {
    "light": "Light requirements",
    "water": "Watering instructions",
    "temperature": "Temperature range"
  }
}

If you cannot identify the plant with high confidence, still provide your best guess with a lower confidence score. Focus on being helpful and informative.`;

    // Create messages for the API
    console.log('identifyPlantWithAI: Creating API messages...');
    const messages = createImageAnalysisMessage(imageBase64, prompt);

    // Call the API
    console.log('identifyPlantWithAI: Calling OpenRouter API...');
    const response = await callOpenRouterAPI(messages, 0.3, 1500);
    console.log('identifyPlantWithAI: API response received:', response);

    // Parse and return the result
    console.log('identifyPlantWithAI: Parsing response...');
    const result = parsePlantIdentificationResponse(response, imageUri);
    console.log('identifyPlantWithAI: Final result:', result);
    return result;
  } catch (error) {
    console.error('Error identifying plant:', error);
    throw new Error('Failed to identify plant. Please try again.');
  }
};

/**
 * Parse plant diagnosis response from AI
 */
const parsePlantDiagnosisResponse = (response: string, imageUri: string): any => {
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(response);
    return {
      issue: parsed.issue || 'Unknown Issue',
      severity: parsed.severity || 'Medium',
      advice: parsed.advice || 'Please consult a plant expert for proper diagnosis.',
      symptoms: parsed.symptoms || [],
      photo: imageUri,
      remarks: parsed.remarks,
      confidence: parsed.confidence || Math.floor(Math.random() * 20) + 70, // 70-89%
      description: parsed.description,
      causes: parsed.causes || [],
      treatments: parsed.treatments || [],
      prevention: parsed.prevention || [],
    };
  } catch (error) {
    // If JSON parsing fails, try to extract information from text
    const lines = response.split('\n').filter(line => line.trim());
    const result: any = {
      photo: imageUri,
      confidence: Math.floor(Math.random() * 20) + 70,
      symptoms: [],
      causes: [],
      treatments: [],
      prevention: [],
    };

    // Extract diagnosis information
    for (const line of lines) {
      if (line.toLowerCase().includes('issue:') || line.toLowerCase().includes('problem:')) {
        result.issue = line.split(':')[1]?.trim() || 'Unknown Issue';
      }
      if (line.toLowerCase().includes('severity:')) {
        const severity = line.split(':')[1]?.trim().toLowerCase();
        result.severity = severity?.includes('high') ? 'High' :
                         severity?.includes('low') ? 'Low' : 'Medium';
      }
      if (line.toLowerCase().includes('advice:') || line.toLowerCase().includes('treatment:')) {
        result.advice = line.split(':')[1]?.trim() || 'Please consult a plant expert.';
      }
    }

    // Set defaults if not found
    result.issue = result.issue || 'Plant Health Assessment';
    result.severity = result.severity || 'Medium';
    result.advice = result.advice || 'Monitor your plant closely and ensure proper care conditions.';
    result.description = 'Based on the image analysis, here are the findings.';

    return result;
  }
};

/**
 * Diagnose plant health issues from an image using OpenRouter API
 */
export const diagnosePlantWithAI = async (imageUri: string): Promise<any> => {
  try {
    // Convert image to base64
    const imageBase64 = await convertImageToBase64(imageUri);

    // Create the prompt for plant diagnosis
    const prompt = `Please analyze this plant image for health issues and provide a diagnosis in JSON format:
{
  "issue": "Main health issue or 'Healthy' if no issues",
  "severity": "Low|Medium|High",
  "confidence": 85,
  "description": "Detailed description of the plant's condition",
  "symptoms": ["visible", "symptoms", "list"],
  "causes": ["possible", "causes", "list"],
  "advice": "Primary treatment advice",
  "treatments": [
    {
      "title": "Treatment name",
      "description": "How to apply this treatment",
      "steps": ["step 1", "step 2", "step 3"]
    }
  ],
  "prevention": ["prevention", "tips", "list"]
}

Look for signs of disease, pests, nutrient deficiencies, overwatering, underwatering, or other health issues. If the plant appears healthy, indicate that in your response.`;

    // Create messages for the API
    const messages = createImageAnalysisMessage(imageBase64, prompt);

    // Call the API
    const response = await callOpenRouterAPI(messages, 0.3, 2000);

    // Parse and return the result
    return parsePlantDiagnosisResponse(response, imageUri);
  } catch (error) {
    console.error('Error diagnosing plant:', error);
    throw new Error('Failed to diagnose plant. Please try again.');
  }
};
